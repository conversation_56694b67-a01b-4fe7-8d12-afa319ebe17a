package model

import "context"

type ProductionFlowUsecase interface {
	Create(ctx context.Context, productionFlow ProductionFlowCreate) (string, error)
	CreateWithActivities(ctx context.Context, productionFlow ProductionFlowCreateWithActivities) (string, error)
	Update(ctx context.Context, productionFlow ProductionFlowUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*ProductionFlow, error)
	GetAll(ctx context.Context) ([]ProductionFlow, error)
	Delete(ctx context.Context, id string) error
	GetWithActivities(ctx context.Context, productionFlowID string) (*ProductionFlowWithActivities, error)
}
