package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
)

type productionFlowUsecase struct {
	repo model.ProductionFlowRepository
}

// Delete implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) Delete(ctx context.Context, id string) error {
	return p.repo.Delete(ctx, id)
}

// GetAll implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) GetAll(ctx context.Context) ([]model.ProductionFlow, error) {
	return p.repo.GetAll(ctx)
}

// GetByProp implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionFlow, error) {
	return p.repo.GetByProp(ctx, prop, value)
}

// GetWithActivities implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) GetWithActivities(ctx context.Context, productionFlowID string) (*model.ProductionFlowWithActivities, error) {
	return p.repo.GetWithActivities(ctx, productionFlowID)
}

func NewProductionFlowUsecase(repo model.ProductionFlowRepository) model.ProductionFlowUsecase {
	return &productionFlowUsecase{
		repo: repo,
	}
}
