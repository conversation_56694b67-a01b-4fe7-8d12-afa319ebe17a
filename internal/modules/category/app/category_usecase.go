package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
)

type categoryUsecase struct {
	repo model.CategoryRepository
}

// Delete implements model.CategoryUsecase.
func (c *categoryUsecase) Delete(ctx context.Context, id string) error {
	return c.repo.Delete(ctx, id)
}

// GetAll implements model.CategoryUsecase.
func (c *categoryUsecase) GetAll(ctx context.Context) ([]model.Category, error) {
	return c.repo.GetAll(ctx)
}

// GetByProp implements model.CategoryUsecase.
func (c *categoryUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Category, error) {
	return c.repo.GetByProp(ctx, prop, value)
}

// GetSubcategories implements model.CategoryUsecase.
func (c *categoryUsecase) GetSubcategories(ctx context.Context, categoryID string) ([]model.Category, error) {
	return c.repo.GetSubcategories(ctx, categoryID)
}

func NewCategoryUsecase(repo model.CategoryRepository) model.CategoryUsecase {
	return &categoryUsecase{
		repo: repo,
	}
}
