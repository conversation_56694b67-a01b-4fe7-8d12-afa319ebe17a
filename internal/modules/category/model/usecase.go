package model

import "context"

type CategoryUsecase interface {
	Create(ctx context.Context, category CategoryCreate) (string, error)
	Update(ctx context.Context, category CategoryUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Category, error)
	GetAll(ctx context.Context) ([]Category, error)
	Delete(ctx context.Context, id string) error
	GetSubcategories(ctx context.Context, categoryID string) ([]Category, error)
	GetSubcategoriesByCode(ctx context.Context, categoryCode string) ([]Category, error)
	GetCategoryWithDetails(ctx context.Context, categoryID string) (*CategoryWithSubcategories, error)
}
