package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements CategoryHandler.
func (c *categoryHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	category, err := c.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category by id")
		return
	}

	rest.SuccessDResponse(w, r, categoryToResult(category), http.StatusOK)
}

// GetAll implements CategoryHandler.
func (c *categoryHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	categories, err := c.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get all categories")
		return
	}

	results := make([]categoryResult, len(categories))
	for i, category := range categories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSubcategories implements CategoryHandler.
func (c *categoryHandler) GetSubcategories(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	subcategories, err := c.useCase.GetSubcategories(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get subcategories")
		return
	}

	results := make([]categoryResult, len(subcategories))
	for i, category := range subcategories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSubcategoriesByCode implements CategoryHandler.
func (c *categoryHandler) GetSubcategoriesByCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	subcategories, err := c.useCase.GetSubcategoriesByCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get subcategories by code")
		return
	}

	results := make([]categoryResult, len(subcategories))
	for i, category := range subcategories {
		results[i] = categoryToResult(&category)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetCategoryWithDetails implements CategoryHandler.
func (c *categoryHandler) GetCategoryWithDetails(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	details, err := c.useCase.GetCategoryWithDetails(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get category with details")
		return
	}

	rest.SuccessDResponse(w, r, categoryWithDetailsToResult(details), http.StatusOK)
}
