package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type OperationHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type operationHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.OperationUsecase
}

func NewOperationHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.OperationUsecase,
) OperationHandler {
	return &operationHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
