meta {
  name: Create Production Flow
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/production-flows
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "code": "PROD_FLOW_001",
    "name": "Standard Production Flow"
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return production flow ID", function() {
    expect(res.getBody()).to.be.a('string');
    bru.setEnvVar("production_flow_id", res.getBody());
  });
}
