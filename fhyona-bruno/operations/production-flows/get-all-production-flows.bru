meta {
  name: Get All Production Flows
  type: http
  seq: 2
}

get {
  url: {{url}}/api/v1/production-flows
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of production flows", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("each production flow should have required fields", function() {
    const flows = res.getBody();
    if (flows.length > 0) {
      expect(flows[0]).to.have.property('id');
      expect(flows[0]).to.have.property('code');
      expect(flows[0]).to.have.property('name');
      expect(flows[0]).to.have.property('created_at');
      expect(flows[0]).to.have.property('updated_at');
    }
  });
}
